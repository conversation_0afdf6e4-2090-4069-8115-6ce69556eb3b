import { View, Text, TouchableOpacity, Alert } from 'react-native'
import React, { useState } from 'react'
import { testConnection, appwriteConfig } from '@/lib/appwrite'

const AppwriteTest = () => {
  const [testing, setTesting] = useState(false)
  const [result, setResult] = useState<string>('')

  const runTest = async () => {
    setTesting(true)
    setResult('')
    
    try {
      // Check environment variables
      console.log('Environment check:')
      console.log('EXPO_PUBLIC_APPWRITE_ENDPOINT:', process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT)
      console.log('EXPO_PUBLIC_APPWRITE_PROJECT_ID:', process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID)
      
      // Check config
      console.log('Config check:')
      console.log('Endpoint:', appwriteConfig.endpoint)
      console.log('Project ID:', appwriteConfig.projectId)
      
      if (!appwriteConfig.endpoint) {
        throw new Error('Endpoint is undefined. Check your .env file.')
      }
      
      if (!appwriteConfig.projectId) {
        throw new Error('Project ID is undefined. Check your .env file.')
      }
      
      // Test connection
      const success = await testConnection()
      
      if (success) {
        setResult('✅ Connection successful!')
        Alert.alert('Success', 'Appwrite connection is working!')
      } else {
        setResult('❌ Connection failed')
        Alert.alert('Error', 'Connection failed. Check console for details.')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setResult(`❌ Error: ${errorMessage}`)
      Alert.alert('Error', errorMessage)
      console.error('Test error:', error)
    } finally {
      setTesting(false)
    }
  }

  return (
    <View className='p-5 bg-white rounded-lg m-5'>
      <Text className='h3-bold text-dark-100 mb-4'>Appwrite Connection Test</Text>
      
      <View className='mb-4'>
        <Text className='paragraph-medium text-gray-100'>Endpoint:</Text>
        <Text className='paragraph-semibold text-dark-100'>{appwriteConfig.endpoint || 'Not set'}</Text>
      </View>
      
      <View className='mb-4'>
        <Text className='paragraph-medium text-gray-100'>Project ID:</Text>
        <Text className='paragraph-semibold text-dark-100'>{appwriteConfig.projectId || 'Not set'}</Text>
      </View>
      
      <TouchableOpacity 
        className='custom-btn mb-4'
        onPress={runTest}
        disabled={testing}
      >
        <Text className='paragraph-semibold text-white'>
          {testing ? 'Testing...' : 'Test Connection'}
        </Text>
      </TouchableOpacity>
      
      {result && (
        <View className='p-3 bg-gray-100 rounded-lg'>
          <Text className='paragraph-medium text-dark-100'>{result}</Text>
        </View>
      )}
    </View>
  )
}

export default AppwriteTest
