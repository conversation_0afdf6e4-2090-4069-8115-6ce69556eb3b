import { appwriteConfig, testConnection } from '@/lib/appwrite'
import React, { useState } from 'react'
import { Alert, Text, TouchableOpacity, View } from 'react-native'

const AppwriteTest = () => {
  const [testing, setTesting] = useState(false)
  const [result, setResult] = useState<string>('')

  const runTest = async () => {
    setTesting(true)
    setResult('Running comprehensive tests...')

    try {
      console.log('=== STARTING COMPREHENSIVE APPWRITE TEST ===')

      // Test connection
      const success = await testConnection()

      if (success) {
        setResult('✅ All tests passed! Check console for details.')
        Alert.alert('Success', 'Appwrite connection is working! Check console for detailed logs.')
      } else {
        setResult('❌ Connection failed - Check console for details')
        Alert.alert('Error', 'Connection failed. Check console for detailed error information.')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setResult(`❌ Error: ${errorMessage}`)
      Alert.alert('Error', errorMessage)
      console.error('Test error:', error)
    } finally {
      setTesting(false)
    }
  }

  const runSeedTest = async () => {
    setTesting(true)
    setResult('Testing database seeding...')

    try {
      // Import the function dynamically to avoid import issues
      const { seedDatabase } = await import('@/lib/appwrite')
      await seedDatabase()

      setResult('✅ Seed test completed! Check console for details.')
      Alert.alert('Success', 'Database seeding test completed!')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setResult(`❌ Seed Error: ${errorMessage}`)
      Alert.alert('Seed Error', errorMessage)
      console.error('Seed test error:', error)
    } finally {
      setTesting(false)
    }
  }

  return (
    <View className='p-5 bg-white rounded-lg m-5'>
      <Text className='h3-bold text-dark-100 mb-4'>Appwrite Connection Test</Text>
      
      <View className='mb-4'>
        <Text className='paragraph-medium text-gray-100'>Endpoint:</Text>
        <Text className='paragraph-semibold text-dark-100'>{appwriteConfig.endpoint || 'Not set'}</Text>
      </View>
      
      <View className='mb-4'>
        <Text className='paragraph-medium text-gray-100'>Project ID:</Text>
        <Text className='paragraph-semibold text-dark-100'>{appwriteConfig.projectId || 'Not set'}</Text>
      </View>
      
      <TouchableOpacity
        className='custom-btn mb-4'
        onPress={runTest}
        disabled={testing}
      >
        <Text className='paragraph-semibold text-white'>
          {testing ? 'Testing...' : 'Test Connection'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        className='custom-btn mb-4'
        onPress={runSeedTest}
        disabled={testing}
        style={{backgroundColor: '#2F9B65'}}
      >
        <Text className='paragraph-semibold text-white'>
          {testing ? 'Testing...' : 'Test Database Seeding'}
        </Text>
      </TouchableOpacity>
      
      {result && (
        <View className='p-3 bg-gray-100 rounded-lg'>
          <Text className='paragraph-medium text-dark-100'>{result}</Text>
        </View>
      )}
    </View>
  )
}

export default AppwriteTest
