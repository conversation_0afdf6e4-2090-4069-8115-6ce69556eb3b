import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native'
import React from 'react'
import { CustomButtonProps } from '@/type'
import cn from 'clsx'

const CustomButton = ({
    onPress,
    title='Click me',
    style,
    leftIcon,
    textStyle,
    isLoading= false,
}: CustomButtonProps) => {
  return (
    <TouchableOpacity className={cn('custon-btn', style)} onPress={onPress}>{leftIcon}

    <View className='flex-center flex-row'>

    {isLoading ? (
        <ActivityIndicator size='small' color='white'/>
    ) : (
        <Text className={cn('paragraph-semibold text-white-100', textStyle)}>{title}</Text>
    )}
    </View>
        
    </TouchableOpacity>
  )
}

export default CustomButton