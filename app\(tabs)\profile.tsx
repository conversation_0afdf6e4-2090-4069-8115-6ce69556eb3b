import ProfileField from '@/components/ProfileField'
import { images } from '@/constants'
import { signOut } from '@/lib/appwrite'
import useAuthStore from '@/store/auth.store'
import { router } from 'expo-router'
import React from 'react'
import { Alert, Image, Text, TouchableOpacity, View } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'

const Profile = () => {
  const { user, setIsAuthenticated, setUser } = useAuthStore()

  const handleSignOut = async () => {
    try {
      await signOut()
      setIsAuthenticated(false)
      setUser(null)
      router.replace('/sign-in')
    } catch (error) {
      Alert.alert('Error', 'Failed to sign out')
    }
  }

  const confirmSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: handleSignOut,
        },
      ]
    )
  }

  return (
    <SafeAreaView className='flex-1 bg-white px-5'>
      <View className='custom-header'>
        <Text className='h3-bold text-dark-100'>Profile</Text>
      </View>

      {/* Profile Avatar Section */}
      <View className='items-center mb-8'>
        <View className='profile-avatar'>
          <Image
            source={user?.avatar ? { uri: user.avatar } : images.avatar}
            className='size-full rounded-full'
            resizeMode='cover'
          />
          <TouchableOpacity className='profile-edit'>
            <Image source={images.pencil} className='size-3' resizeMode='contain' tintColor='white' />
          </TouchableOpacity>
        </View>
        <Text className='h3-bold text-dark-100 mt-4'>{user?.name || 'User Name'}</Text>
        <Text className='paragraph-medium text-gray-100'>{user?.email || '<EMAIL>'}</Text>
      </View>

      {/* Profile Fields */}
      <View className='flex-1'>
        <ProfileField
          label='Full Name'
          value={user?.name || 'Not provided'}
          icon={images.user}
        />
        <ProfileField
          label='Email'
          value={user?.email || 'Not provided'}
          icon={images.envelope}
        />
        <ProfileField
          label='Phone'
          value='****** 567 8900'
          icon={images.phone}
        />
        <ProfileField
          label='Location'
          value='New York, USA'
          icon={images.location}
        />
      </View>

      {/* Sign Out Button */}
      <TouchableOpacity
        className='flex-row items-center justify-center bg-error rounded-full p-4 mb-8'
        onPress={confirmSignOut}
      >
        <Image source={images.logout} className='size-5 mr-3' resizeMode='contain' tintColor='white' />
        <Text className='paragraph-semibold text-white'>Sign Out</Text>
      </TouchableOpacity>
    </SafeAreaView>
  )
}

export default Profile