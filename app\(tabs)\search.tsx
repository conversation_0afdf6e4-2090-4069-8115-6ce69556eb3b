import { View, Text, TouchableOpacity, Alert, ScrollView } from 'react-native'
import React, { useState } from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import seed from '@/lib/seed'
import { testConnection, testNetworkConnectivity } from '@/lib/appwrite'

const Search = () => {
  const [isSeeding, setIsSeeding] = useState(false)
  const [seedResult, setSeedResult] = useState<string>('')

  const handleSeed = async () => {
    setIsSeeding(true)
    setSeedResult('Starting seeding process...')

    try {
      console.log('=== STARTING DATABASE SEEDING ===')

      // Step 1: Test network connectivity
      console.log('1. Testing network connectivity...')
      setSeedResult('1. Testing network connectivity...')
      const networkOk = await testNetworkConnectivity()

      if (!networkOk) {
        throw new Error('Network connectivity failed. Check your internet connection.')
      }

      // Step 2: Test Appwrite connection
      console.log('2. Testing Appwrite connection...')
      setSeedResult('2. Testing Appwrite connection...')
      const connectionOk = await testConnection()

      if (!connectionOk) {
        throw new Error('Appwrite connection failed. Check your configuration.')
      }

      // Step 3: Run seeding
      console.log('3. Starting database seeding...')
      setSeedResult('3. Seeding database... (this may take a while)')
      await seed()

      setSeedResult('✅ Database seeding completed successfully!')
      Alert.alert('Success', 'Database has been seeded successfully!')
      console.log('✅ Database seeding completed successfully!')

    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      setSeedResult(`❌ Seeding failed: ${errorMessage}`)
      Alert.alert('Seeding Failed', errorMessage)
      console.error('❌ Seeding failed:', error)

      // Log specific error details
      if (error.message?.includes('Network request failed')) {
        console.error('This is a network connectivity issue. Check:')
        console.error('1. Internet connection')
        console.error('2. Appwrite endpoint URL')
        console.error('3. Firewall settings')
      }
    } finally {
      setIsSeeding(false)
    }
  }

  return (
    <SafeAreaView className='flex-1 bg-white px-5'>
      <ScrollView>
        <Text className='h3-bold text-dark-100 mb-5'>Search & Database Management</Text>

        {/* Seed Database Section */}
        <View className='bg-white rounded-lg p-5 mb-5 shadow-md'>
          <Text className='paragraph-bold text-dark-100 mb-3'>Database Seeding</Text>
          <Text className='body-regular text-gray-100 mb-4'>
            This will populate your database with sample menu items, categories, and customizations.
          </Text>

          <TouchableOpacity
            className='custom-btn mb-4'
            onPress={handleSeed}
            disabled={isSeeding}
          >
            <Text className='paragraph-semibold text-white'>
              {isSeeding ? 'Seeding...' : 'Seed Database'}
            </Text>
          </TouchableOpacity>

          {seedResult && (
            <View className='p-3 bg-gray-100 rounded-lg'>
              <Text className='body-medium text-dark-100'>{seedResult}</Text>
            </View>
          )}
        </View>

        {/* Search functionality can be added here later */}
        <View className='bg-white rounded-lg p-5 shadow-md'>
          <Text className='paragraph-bold text-dark-100 mb-3'>Search</Text>
          <Text className='body-regular text-gray-100'>
            Search functionality will be implemented here.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

export default Search