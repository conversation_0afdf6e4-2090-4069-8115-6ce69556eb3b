import { Create<PERSON>serParams, SignInParams } from "@/type";
import { Account, Avatars, Client, Databases, ID, Query, Storage } from "react-native-appwrite";

export const appwriteConfig = {
    endpoint: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || "https://fra.cloud.appwrite.io/v1",
    projectId: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID || "6871201e003c0cd8c990",
    platform: "com.fizza.compony",
    databaseId: "6871224b00279f846413",
    bucketId:'68727b72001f969c314d',
    userCollectionId: "687122740029d54a7899",
    categoriesCollectionId: '687275b4001e707b4bea',
    menuCollectionId: '687276c20020078e185f',
    customizationsCollectionId: '68727890003291aa0fb0',
    menuCustomizationsCollectionId: '68727a37001ede6c0572',
}

// Debug: Log configuration on load
console.log('=== APPWRITE CONFIGURATION ===');
console.log('Environment Variables:');
console.log('  EXPO_PUBLIC_APPWRITE_ENDPOINT:', process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT);
console.log('  EXPO_PUBLIC_APPWRITE_PROJECT_ID:', process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID);
console.log('Final Config:');
console.log('  endpoint:', appwriteConfig.endpoint);
console.log('  projectId:', appwriteConfig.projectId);
console.log('  platform:', appwriteConfig.platform);
console.log('Validation:');
console.log('  hasEndpoint:', !!appwriteConfig.endpoint);
console.log('  hasProjectId:', !!appwriteConfig.projectId);
console.log('================================');

export const client = new Client();

// Initialize client with error handling
try {
    console.log('Initializing Appwrite client...');

    if (!appwriteConfig.endpoint) {
        throw new Error('❌ Appwrite endpoint is undefined. Check your .env file.');
    }

    if (!appwriteConfig.projectId) {
        throw new Error('❌ Appwrite project ID is undefined. Check your .env file.');
    }

    client
        .setEndpoint(appwriteConfig.endpoint)
        .setProject(appwriteConfig.projectId)
        .setPlatform(appwriteConfig.platform);

    console.log('✅ Appwrite client initialized successfully');
} catch (error) {
    console.error('❌ Failed to initialize Appwrite client:', error);
    throw error;
}

   
   export const account = new Account(client);
   export const databases = new Databases(client);
   export const storage = new Storage(client);
   const avatars = new Avatars(client);

export const createUser = async ({email, password, name}: CreateUserParams) => {
    try {
        const newAccount = await account.create(ID.unique(), email, password, name);
        if(!newAccount) throw Error;
        const avatarUrl = avatars.getInitialsURL(name);

        await signIn({email, password});

        return await databases.createDocument(
            appwriteConfig.databaseId,
            appwriteConfig.userCollectionId,
            ID.unique(),
            {
                email,
                name,
                accountId: newAccount.$id,
                avatar: avatarUrl
            }
        );

    } catch (error) {
        throw new Error(error as string);
    }
}

export const signIn = async({email, password}: SignInParams) => {
    try {
        const session = await account.createEmailPasswordSession(email, password);
        if(!session) throw Error;

        return session;
    } catch (error) {
        throw new Error(error as string);
    }
}


export const getCurrentUser = async () => {
    try {
        const currentAccount = await account.get();
        if(!currentAccount) throw Error;

        const currentUser = await databases.listDocuments(
            appwriteConfig.databaseId,
            appwriteConfig.userCollectionId,
            [Query.equal("accountId", currentAccount.$id)]
        );

        if(!currentUser) throw Error;

        return currentUser.documents[0];
    } catch (error) {
        console.log(error);
        return null;
    }
}

export const signOut = async () => {
    try {
        const session = await account.deleteSession("current");
        return session;
    } catch (error) {
        throw new Error(error as string);
    }
}

// Debug function to test Appwrite connection
export const testConnection = async () => {
    try {
        console.log('=== APPWRITE CONNECTION TEST ===');
        console.log('1. Environment Variables:');
        console.log('   EXPO_PUBLIC_APPWRITE_ENDPOINT:', process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT);
        console.log('   EXPO_PUBLIC_APPWRITE_PROJECT_ID:', process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID);

        console.log('2. Config Values:');
        console.log('   Endpoint:', appwriteConfig.endpoint);
        console.log('   Project ID:', appwriteConfig.projectId);
        console.log('   Platform:', appwriteConfig.platform);

        if (!appwriteConfig.endpoint) {
            throw new Error('❌ Endpoint is undefined - check your .env file');
        }

        if (!appwriteConfig.projectId) {
            throw new Error('❌ Project ID is undefined - check your .env file');
        }

        console.log('3. Testing connection...');

        // Try to get account (this will fail if not authenticated, but should not give network error)
        const health = await account.get();
        console.log('✅ Connection successful! User is authenticated:', health);
        return true;
    } catch (error: any) {
        console.log('4. Connection test result:');

        if (error.code === 401) {
            console.log('✅ Connection OK - User not authenticated (expected)');
            console.log('   This means Appwrite is reachable but no user is signed in');
            return true;
        } else if (error.message?.includes('Network request failed')) {
            console.error('❌ Network Error - Cannot reach Appwrite server');
            console.error('   Check your internet connection and endpoint URL');
            console.error('   Error details:', error);
            return false;
        } else {
            console.error('❌ Other Error:', error);
            return false;
        }
    }
}

// Test basic network connectivity
export const testNetworkConnectivity = async () => {
    try {
        console.log('=== NETWORK CONNECTIVITY TEST ===');
        console.log(`Testing endpoint: ${appwriteConfig.endpoint}/health`);

        // Test basic fetch to Appwrite health endpoint
        const response = await fetch(`${appwriteConfig.endpoint}/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        console.log('✅ Network connectivity OK');
        console.log('   Health response:', data);
        return true;
    } catch (error) {
        console.error('❌ Network connectivity failed:', error);
        console.error('   Error details:', {
            message: error instanceof Error ? error.message : 'Unknown error',
            endpoint: `${appwriteConfig.endpoint}/health`
        });
        return false;
    }
}

// Function to seed database data
export const seedDatabase = async () => {
    try {
        console.log('=== DATABASE SEEDING ===');

        // Test network first
        console.log('1. Testing network connectivity...');
        const networkOk = await testNetworkConnectivity();
        if (!networkOk) {
            throw new Error('Network connectivity failed');
        }

        // Test connection
        console.log('2. Testing Appwrite connection...');
        const isConnected = await testConnection();
        if (!isConnected) {
            throw new Error('Cannot connect to Appwrite. Please check your configuration.');
        }

        console.log('3. Ready to seed database...');
        // Add your seeding logic here

        console.log('✅ Database seeding completed successfully!');
        return true;
    } catch (error) {
        console.error('❌ Failed to seed database:', error);
        throw error;
    }
}

// Simple diagnostic function to test everything
export const runDiagnostics = async () => {
    console.log('🔍 RUNNING COMPLETE APPWRITE DIAGNOSTICS');
    console.log('==========================================');

    try {
        // Test 1: Environment Variables
        console.log('📋 Test 1: Environment Variables');
        console.log('   EXPO_PUBLIC_APPWRITE_ENDPOINT:', process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT ? '✅ Set' : '❌ Missing');
        console.log('   EXPO_PUBLIC_APPWRITE_PROJECT_ID:', process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID ? '✅ Set' : '❌ Missing');

        // Test 2: Configuration
        console.log('📋 Test 2: Configuration Values');
        console.log('   Endpoint:', appwriteConfig.endpoint ? '✅ Valid' : '❌ Invalid');
        console.log('   Project ID:', appwriteConfig.projectId ? '✅ Valid' : '❌ Invalid');

        // Test 3: Network Connectivity
        console.log('📋 Test 3: Network Connectivity');
        const networkOk = await testNetworkConnectivity();
        console.log('   Network:', networkOk ? '✅ OK' : '❌ Failed');

        // Test 4: Appwrite Connection
        console.log('📋 Test 4: Appwrite Connection');
        const connectionOk = await testConnection();
        console.log('   Connection:', connectionOk ? '✅ OK' : '❌ Failed');

        console.log('==========================================');

        if (networkOk && connectionOk) {
            console.log('🎉 ALL TESTS PASSED! Appwrite is ready to use.');
            return true;
        } else {
            console.log('❌ SOME TESTS FAILED. Check the details above.');
            return false;
        }

    } catch (error) {
        console.error('❌ DIAGNOSTICS FAILED:', error);
        return false;
    }
}