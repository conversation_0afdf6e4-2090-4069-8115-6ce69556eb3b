import CustomButton from '@/components/CustomButton'
import CustomInput from '@/components/CustomInput'
import { createUser } from '@/lib/appwrite'
import useAuthStore from '@/store/auth.store'
import { Link, router } from 'expo-router'
import React, { useState } from 'react'
import { Alert, Text, View } from 'react-native'

const SignUp = () => {
    const { fetchAuthenticatedUser } = useAuthStore()
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [form, setForm] = useState({name: '',email: '' , password:''})

    const {name, email , password} = form;

    const sumbit = async () => {
        if(!email || !password || !name) return Alert.alert('Error', 'Please fill in all fields')

        setIsSubmitting(true)

        try {
            await createUser({
                 email,
                 password,
                 name
            });
            await fetchAuthenticatedUser();

            Alert.alert('Success', 'Account created successfully!')
            router.replace('/');
        } catch (error) {
            Alert.alert('Error', error instanceof Error ? error.message : 'Something went wrong')
        } finally {
            setIsSubmitting(false)
        }
    }

  return (
    <View className='gap-10 bg-white rounded-lg p-5 mt-5'>
        <CustomInput 
            placeholder='Enter your email'
            label='Email'
            // secureTextEntry={false}
            keyboardType='email-address'
            onChangeText={(text) => setForm((prev) => ({ ...prev, email: text}))}
            value={form.email}
        />
        <CustomInput
            placeholder='Enter your fullname'
            label='Full name'
            secureTextEntry={false}
            onChangeText={(text) => setForm((prev) => ({...prev, name: text}))}
            value={form.name}
        />
        <CustomInput 
            placeholder='Enter your password'
            label='password'
            secureTextEntry={true}
            onChangeText={(text) => setForm((prev) => ({...prev, password: text}))}
            value={form.password}
        />
        <CustomButton
        title='Sign Up'
        isLoading={isSubmitting}
        onPress={sumbit}
        />

        <View className='flex justify-center mt-5 flex-row gap-2'>
            <Text className='base-regular text-gray-100'>
                Already have account?
            </Text>
            <Link href="/sign-in" className='base-bold text-primary'>
            Sign In
            </Link>
        </View>
    </View>
  )
}

export default SignUp
