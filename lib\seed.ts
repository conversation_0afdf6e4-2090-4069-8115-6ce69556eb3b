import { ID } from "react-native-appwrite";
import { appwriteConfig, databases, storage } from "./appwrite";
import dummyData from "./data";

interface Category {
    name: string;
    description: string;
}

interface Customization {
    name: string;
    price: number;
    type: "topping" | "side" | "size" | "crust" | string; // extend as needed
}

interface MenuItem {
    name: string;
    description: string;
    image_url: string;
    price: number;
    rating: number;
    calories: number;
    protein: number;
    category_name: string;
    customizations: string[]; // list of customization names
}

interface DummyData {
    categories: Category[];
    customizations: Customization[];
    menu: MenuItem[];
}

// ensure dummyData has correct shape
const data = dummyData as DummyData;

async function clearAll(collectionId: string): Promise<void> {
    const list = await databases.listDocuments(
        appwriteConfig.databaseId,
        collectionId
    );

    await Promise.all(
        list.documents.map((doc) =>
            databases.deleteDocument(appwriteConfig.databaseId, collectionId, doc.$id)
        )
    );
}

async function clearStorage(): Promise<void> {
    const list = await storage.listFiles(appwriteConfig.bucketId);

    await Promise.all(
        list.files.map((file) =>
            storage.deleteFile(appwriteConfig.bucketId, file.$id)
        )
    );
}

async function uploadImageToStorage(imageUrl: string) {
    try {
        console.log(`     📸 Uploading image: ${imageUrl.split("/").pop()}`);
        const response = await fetch(imageUrl);

        if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }

        const blob = await response.blob();

        const fileObj = {
            name: imageUrl.split("/").pop() || `file-${Date.now()}.jpg`,
            type: blob.type,
            size: blob.size,
            uri: imageUrl,
        };

        const file = await storage.createFile(
            appwriteConfig.bucketId,
            ID.unique(),
            fileObj
        );

        const viewUrl = storage.getFileViewURL(appwriteConfig.bucketId, file.$id);
        console.log(`     ✅ Image uploaded successfully`);
        return viewUrl;
    } catch (error) {
        console.error(`     ❌ Failed to upload image: ${imageUrl}`);
        console.error(`     Error:`, error);
        throw error;
    }
}

async function seed(): Promise<void> {
    try {
        console.log('🚀 Starting database seeding process...');

        // 1. Clear all existing data
        console.log('📝 Step 1: Clearing existing data...');
        console.log('   - Clearing categories...');
        await clearAll(appwriteConfig.categoriesCollectionId);

        console.log('   - Clearing customizations...');
        await clearAll(appwriteConfig.customizationsCollectionId);

        console.log('   - Clearing menu items...');
        await clearAll(appwriteConfig.menuCollectionId);

        console.log('   - Clearing menu customizations...');
        await clearAll(appwriteConfig.menuCustomizationsCollectionId);

        console.log('   - Clearing storage...');
        await clearStorage();

        // 2. Create Categories
        console.log('📝 Step 2: Creating categories...');
        const categoryMap: Record<string, string> = {};
        for (const cat of data.categories) {
            console.log(`   - Creating category: ${cat.name}`);
            const doc = await databases.createDocument(
                appwriteConfig.databaseId,
                appwriteConfig.categoriesCollectionId,
                ID.unique(),
                cat
            );
            categoryMap[cat.name] = doc.$id;
        }
        console.log(`   ✅ Created ${data.categories.length} categories`);

        // 3. Create Customizations
        console.log('📝 Step 3: Creating customizations...');
        const customizationMap: Record<string, string> = {};
        for (const cus of data.customizations) {
            console.log(`   - Creating customization: ${cus.name} (${cus.type})`);
            const doc = await databases.createDocument(
                appwriteConfig.databaseId,
                appwriteConfig.customizationsCollectionId,
                ID.unique(),
                {
                    name: cus.name,
                    price: cus.price,
                    type: cus.type,
                }
            );
            customizationMap[cus.name] = doc.$id;
        }
        console.log(`   ✅ Created ${data.customizations.length} customizations`);

    // 4. Create Menu Items
    const menuMap: Record<string, string> = {};
    for (const item of data.menu) {
        const uploadedImage = await uploadImageToStorage(item.image_url);

        const doc = await databases.createDocument(
            appwriteConfig.databaseId,
            appwriteConfig.menuCollectionId,
            ID.unique(),
            {
                name: item.name,
                description: item.description,
                image_url: uploadedImage,
                price: item.price,
                rating: item.rating,
                calories: item.calories,
                protein: item.protein,
                categories: categoryMap[item.category_name],
            }
        );

        menuMap[item.name] = doc.$id;

        // 5. Create menu_customizations
        for (const cusName of item.customizations) {
            await databases.createDocument(
                appwriteConfig.databaseId,
                appwriteConfig.menuCustomizationsCollectionId,
                ID.unique(),
                {
                    menu: doc.$id,
                    customizations: customizationMap[cusName],
                }
            );
        }
    }

        console.log("🎉 Database seeding completed successfully!");
        console.log(`📊 Summary:`);
        console.log(`   - Categories: ${data.categories.length}`);
        console.log(`   - Customizations: ${data.customizations.length}`);
        console.log(`   - Menu Items: ${data.menu.length}`);

    } catch (error) {
        console.error('❌ Database seeding failed:', error);

        // Provide specific error guidance
        if (error instanceof Error) {
            if (error.message.includes('Network request failed')) {
                console.error('🔧 This appears to be a network connectivity issue.');
                console.error('   Please check:');
                console.error('   1. Your internet connection');
                console.error('   2. Appwrite endpoint URL in .env file');
                console.error('   3. Firewall settings');
            } else if (error.message.includes('Collection with the requested ID could not be found')) {
                console.error('🔧 Collection not found error.');
                console.error('   Please check:');
                console.error('   1. Collection IDs in appwrite.ts');
                console.error('   2. Database exists in Appwrite console');
                console.error('   3. Collections are created in Appwrite console');
            } else if (error.message.includes('Bucket with the requested ID could not be found')) {
                console.error('🔧 Storage bucket not found error.');
                console.error('   Please check:');
                console.error('   1. Bucket ID in appwrite.ts');
                console.error('   2. Storage bucket exists in Appwrite console');
            }
        }

        throw error; // Re-throw to be handled by the calling function
    }
}

export default seed;