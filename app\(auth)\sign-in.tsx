import CustomButton from '@/components/CustomButton'
import CustomInput from '@/components/CustomInput'
import { signIn } from '@/lib/appwrite'
import useAuthStore from '@/store/auth.store'
import { Link, router } from 'expo-router'
import React, { useState } from 'react'
import { Alert, Text, View } from 'react-native'

const SignIn = () => {
    const { fetchAuthenticatedUser } = useAuthStore()
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [form, setForm] = useState({email: '' , password:''})

    const {email, password} = form;

    const sumbit = async () => {
        if(!email || !password) return Alert.alert('Error', 'Please fill in all fields')

        setIsSubmitting(true)

        try {
            await signIn({ email, password });
            await fetchAuthenticatedUser();

            Alert.alert('Success', 'You have successfully signed in')
            router.replace('/');
        } catch (error) {
            Alert.alert('Error', error instanceof Error ? error.message : 'Something went wrong')
        } finally {
            setIsSubmitting(false)
        }
    }

  return (
    <View className='gap-10 bg-white rounded-lg p-5 mt-5'>
        <CustomInput 
            placeholder='Enter your email'
            label='Email'
            // secureTextEntry={false}
            keyboardType='email-address'
            onChangeText={(text) => setForm((prev) => ({ ...prev, email: text}))}
            value={form.email}
        />
        <CustomInput 
            placeholder='Enter your password'
            label='password'
            secureTextEntry={true}
            onChangeText={(text) => setForm((prev) => ({...prev, password: text}))}
            value={form.password}
        />
        <CustomButton
        title='Sign In'
        isLoading={isSubmitting}
        onPress={sumbit}
        />

        <View className='flex justify-center mt-5 flex-row gap-2'>
            <Text className='base-regular text-gray-100'>
                Don't have an account?
            </Text>
            <Link href="/sign-up" className='base-bold text-primary'>
            Sign Up
            </Link>
        </View>
    </View>
  )
}

export default SignIn
